// src/components/ReportGeneratorButton.js

import React, { useState, useMemo } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const ReportGeneratorButton = ({ reportData, isLoading }) => {
  // console.log("ReportGeneratorButton props:", { reportData, isLoading });
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedYear, setSelectedYear] = useState('all');

  const availableYears = useMemo(() => {
    if (!reportData) return [];
    const years = new Set();
    reportData.forEach(item => {
      if (item.formData) {
        item.formData.forEach(submission => {
          // *** ASSUMPTION: Your date field is named 'createdAt' ***
          // Find the field object that contains the createdAt key.
          const dateField = submission.find(field => Object.keys(field)[0] === 'createdAt');
          if (dateField && dateField.createdAt) {
            try {
              const year = new Date(dateField.createdAt).getFullYear();
              if (!isNaN(year)) {
                 years.add(year);
              }
            } catch (e) {
              console.error("Could not parse date:", dateField.createdAt);
            }
          }
        });
      }
    });
    // Return a sorted array of unique years, newest first
    return Array.from(years).sort((a, b) => b - a);
  }, [reportData]);

  // This powerful memo hook filters AND groups the data based on the selected year.
  // It only recalculates when the raw data or the selected year changes.
  const filteredAndGroupedData = useMemo(() => {
    if (!reportData) return {};

    return reportData.reduce((acc, item) => {
      const { pageID } = item;
      if (!acc[pageID]) {
        acc[pageID] = [];
      }

      if (item.formData && item.formData.length > 0) {
        // Filter submissions by the selected year before adding them
        const filteredSubmissions = item.formData.filter(submission => {
          if (selectedYear === 'all') {
            return true; // Include all if 'all' is selected
          }
          const dateField = submission.find(field => Object.keys(field)[0] === 'createdAt');
          if (dateField && dateField.createdAt) {
            try {
              return new Date(dateField.createdAt).getFullYear() === parseInt(selectedYear, 10);
            } catch(e) {
              return false;
            }
          }
          return false; // Exclude if no date field is found
        });
        
        if (filteredSubmissions.length > 0) {
          acc[pageID].push(...filteredSubmissions);
        }
      }
      return acc;
    }, {});
  }, [reportData, selectedYear]);

  // Enhanced PDF generation with professional formatting
  const addHeader = (doc, pageNumber = 1) => {
    const pageWidth = doc.internal.pageSize.width;

    // Header background
    doc.setFillColor(15, 35, 75); // Dark blue
    doc.rect(0, 0, pageWidth, 35, 'F');

    // Company logo placeholder (you can replace with actual logo)
    doc.setFillColor(255, 255, 255);
    doc.circle(25, 17.5, 8, 'F');
    doc.setFontSize(8);
    doc.setTextColor(15, 35, 75);
    doc.text('NS', 22, 20);

    // Company name and title
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text('NS Academic Solutions', 40, 15);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text('Academic Performance Assessment & Appraisal Report', 40, 25);

    // Page number
    doc.setFontSize(8);
    doc.text(`Page ${pageNumber}`, pageWidth - 30, 20);

    return 45; // Return Y position after header
  };

  const addFooter = (doc) => {
    const pageHeight = doc.internal.pageSize.height;
    const pageWidth = doc.internal.pageSize.width;

    // Footer line
    doc.setDrawColor(200, 200, 200);
    doc.line(14, pageHeight - 25, pageWidth - 14, pageHeight - 25);

    // Footer text
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.setFont('helvetica', 'normal');
    doc.text('Generated by NS Academic Solutions', 14, pageHeight - 15);
    doc.text(`Generated on: ${new Date().toLocaleDateString('en-IN')} at ${new Date().toLocaleTimeString('en-IN')}`, 14, pageHeight - 8);

    // Confidential notice
    const confidentialText = 'CONFIDENTIAL - For Internal Use Only';
    const textWidth = doc.getTextWidth(confidentialText);
    doc.text(confidentialText, pageWidth - textWidth - 14, pageHeight - 15);
  };

  const formatFieldName = (fieldName) => {
    // Convert camelCase or snake_case to proper title case
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  };

  const getCategoryTitle = (pageId) => {
    // Enhanced category mapping with more comprehensive titles
    const categoryMap = {
      'c4e293e9-1f5c-4edd-a3e5-fa0dfc23e566': 'Mandatory Teachers Training (SWAYAM)',
      '2544a712-bd7d-46ee-8ca8-12c51f8bed35': 'Faculty Development Programs',
      '5c97fdc9-12a4-4551-af1c-b9962e962be3': 'M.Tech Project Supervision',
      '5f7b6f6d-fc1a-4086-85ff-adc1c3a4ffd7': 'B.Tech Project Supervision',
      '08f9f04e-eb8e-4a10-9779-e2c93f10c8bd': 'Ph.D. Scholar Supervision',
      // Add more mappings based on GlobalArrays.js
    };
    return categoryMap[pageId] || `Academic Activity (${pageId.substring(0, 8)}...)`;
  };

  // Enhanced data validation and formatting
  const validateAndFormatData = (submissions) => {
    return submissions.filter(submission => {
      // Filter out incomplete or invalid submissions
      return submission && Array.isArray(submission) && submission.length > 0;
    }).map(submission => {
      // Ensure all fields have proper values
      return submission.map(field => {
        const [key, value] = Object.entries(field)[0];
        return {
          [key]: value !== undefined && value !== null ? String(value).trim() : '-'
        };
      });
    });
  };

  const handleGeneratePdf = () => {
    const dataToProcess = filteredAndGroupedData;
    if (Object.keys(dataToProcess).every(key => dataToProcess[key].length === 0)) {
        alert(`No data available for the year ${selectedYear}.`);
        return;
    }

    setIsGenerating(true);

    try {
      const doc = new jsPDF();
      let pageNumber = 1;
      let currentY = addHeader(doc, pageNumber);

      // Report title and metadata
      currentY += 10;
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(15, 35, 75);
      const reportTitle = `Faculty Appraisal Report`;
      doc.text(reportTitle, 14, currentY);

      currentY += 15;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(80, 80, 80);
      doc.text(`Report Period: ${selectedYear === 'all' ? 'All Years' : `Academic Year ${selectedYear}`}`, 14, currentY);

      currentY += 8;
      doc.text(`Total Categories: ${Object.keys(dataToProcess).length}`, 14, currentY);

      const totalSubmissions = Object.values(dataToProcess).reduce((sum, submissions) => sum + submissions.length, 0);
      doc.text(`Total Submissions: ${totalSubmissions}`, 14, currentY + 8);

      currentY += 25;

      // Executive Summary
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(15, 35, 75);
      doc.text('Executive Summary', 14, currentY);

      currentY += 10;
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(60, 60, 60);
      const summaryText = `This report contains comprehensive faculty appraisal data across ${Object.keys(dataToProcess).length} categories. The data includes professional development activities, research contributions, and academic achievements for the specified period.`;
      const splitSummary = doc.splitTextToSize(summaryText, 180);
      doc.text(splitSummary, 14, currentY);
      currentY += splitSummary.length * 5 + 15;

      // Process each category
      Object.entries(dataToProcess).forEach(([pageId, submissions], index) => {
        if (submissions.length === 0) return;

        // Validate and format the data
        const validatedSubmissions = validateAndFormatData(submissions);
        if (validatedSubmissions.length === 0) return;

        // Check if we need a new page
        if (currentY > 220) {
          addFooter(doc);
          doc.addPage();
          pageNumber++;
          currentY = addHeader(doc, pageNumber) + 10;
        }

        // Category header
        doc.setFillColor(240, 248, 255);
        doc.rect(14, currentY - 5, 182, 20, 'F');

        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(15, 35, 75);
        doc.text(`${index + 1}. ${getCategoryTitle(pageId)}`, 18, currentY + 5);

        doc.setFontSize(9);
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(100, 100, 100);
        doc.text(`${validatedSubmissions.length} submission${validatedSubmissions.length !== 1 ? 's' : ''}`, 18, currentY + 12);

        currentY += 25;

        // Prepare table data using validated submissions
        const headers = Array.from(new Set(
          validatedSubmissions.flatMap(record =>
            record.map(field => Object.keys(field)[0])
          )
        )).sort().map(header => formatFieldName(header));

        const rowsAsObjects = validatedSubmissions.map(record =>
          record.reduce((acc, field) => {
            const [key, value] = Object.entries(field)[0];
            acc[key] = value;
            return acc;
          }, {})
        );

        const tableBody = rowsAsObjects.map(row =>
          headers.map(header => {
            const originalKey = Object.keys(row).find(key =>
              formatFieldName(key) === header
            );
            const value = row[originalKey];

            // Format dates nicely
            if (originalKey === 'createdAt' && value) {
              try {
                return new Date(value).toLocaleDateString('en-IN');
              } catch (e) {
                return value || '-';
              }
            }

            return value || '-';
          })
        );

        // Generate table
        autoTable(doc, {
          head: [headers],
          body: tableBody,
          startY: currentY,
          theme: 'striped',
          headStyles: {
            fillColor: [15, 35, 75],
            textColor: [255, 255, 255],
            fontSize: 9,
            fontStyle: 'bold',
            halign: 'center'
          },
          bodyStyles: {
            fontSize: 8,
            cellPadding: 3,
            valign: 'middle'
          },
          alternateRowStyles: {
            fillColor: [248, 250, 252]
          },
          columnStyles: {
            0: { cellWidth: 'auto', minCellWidth: 20 }
          },
          margin: { left: 14, right: 14 },
          tableWidth: 'auto',
          styles: {
            overflow: 'linebreak',
            cellWidth: 'wrap'
          }
        });

        currentY = doc.lastAutoTable.finalY + 15;
      });

      // Add footer to last page
      addFooter(doc);

      // Save the PDF
      const fileName = `Faculty_Appraisal_Report_${selectedYear === 'all' ? 'All_Years' : selectedYear}_${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('An error occurred while generating the PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };
  
  const hasData = reportData && reportData.length > 0;

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!hasData) return null;

    const totalCategories = Object.keys(filteredAndGroupedData).length;
    const totalSubmissions = Object.values(filteredAndGroupedData).reduce((sum, submissions) => sum + submissions.length, 0);
    const categoriesWithData = Object.values(filteredAndGroupedData).filter(submissions => submissions.length > 0).length;

    return {
      totalCategories,
      totalSubmissions,
      categoriesWithData
    };
  }, [filteredAndGroupedData, hasData]);

  return (
    <div className="space-y-6">
      {/* Report Summary Card */}
      {hasData && summaryStats && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Report Summary
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="text-2xl font-bold text-blue-600">{summaryStats.totalSubmissions}</div>
              <div className="text-sm text-gray-600">Total Submissions</div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="text-2xl font-bold text-green-600">{summaryStats.categoriesWithData}</div>
              <div className="text-sm text-gray-600">Categories with Data</div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-blue-100">
              <div className="text-2xl font-bold text-purple-600">{summaryStats.totalCategories}</div>
              <div className="text-sm text-gray-600">Total Categories</div>
            </div>
          </div>
        </div>
      )}

      {/* Report Generation Controls */}
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Generate Faculty Appraisal Report
          </h2>
          <p className="text-blue-100 text-sm mt-1">
            Create a comprehensive PDF report of faculty performance data
          </p>
        </div>

        <div className="p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-end gap-4">
            <div className="flex-grow w-full sm:w-auto">
              <label htmlFor="year-select" className="block text-sm font-medium text-gray-700 mb-2">
                Select Report Period
              </label>
              <select
                id="year-select"
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                disabled={!hasData || isLoading || isGenerating}
                className="w-full sm:w-64 p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white"
              >
                <option value="all">All Years</option>
                {availableYears.map(year => (
                  <option key={year} value={year}>Academic Year {year}</option>
                ))}
              </select>
              {selectedYear !== 'all' && (
                <p className="text-xs text-gray-500 mt-1">
                  Showing data for academic year {selectedYear}
                </p>
              )}
            </div>

            <button
              onClick={handleGeneratePdf}
              disabled={!hasData || isLoading || isGenerating}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold py-3 px-8 rounded-lg shadow-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed disabled:shadow-none transform hover:scale-105 disabled:hover:scale-100 flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Loading Data...
                </>
              ) : isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating PDF...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download Report
                </>
              )}
            </button>
          </div>

          {!hasData && !isLoading && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="text-yellow-800 text-sm">No data available for report generation.</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportGeneratorButton;
.mainHome{
    /* margin-top: 10vh; */
    height: 90vh;
    /* background-color: rgb(173,216,230,0.2) */
    /* background-color:rgb(178, 190, 181); */
    background-color:rgb(115, 147, 179,0.3);
    display: flex;
    flex-direction: column;
    gap: 1vw;
}
.mainHome1{
    /* border: 2px solid red;  */
    width: 70vw;
    margin-left: auto;
    margin-right: auto;
    height: 20vh;
    background-color: white;
}
.mainHome2{
    /* border: 1px solid gray; */
    width: 70vw;
    margin-left: auto;
    margin-right: auto;
    height: 66vh;
    border-top-left-radius:20px ;
    border-top-right-radius:20px ;
    padding: 1vw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    background-color: white;
}
.mainHome3{
    display: flex;
    justify-content:space-between;
    align-items: center;
}
.titleName{
    font-size: 24px; 
}